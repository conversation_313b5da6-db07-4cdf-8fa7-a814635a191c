# -*- coding: utf-8 -*-
"""
Dashboard Serializers Module

This module contains serializers for dashboard-related API endpoints.
"""
from rest_framework import serializers
from MCDC.serializers import TcdNewsSerializer, TcdNewscategorySerializer
from MCDC.models import TcdNewscategory

class TcdNewsListSerializer(serializers.ModelSerializer):
    """
    Serializer for TcdNews model in list view - excludes large data fields
    """
    newscategory = serializers.SerializerMethodField()
    
    class Meta:
        model = TcdNewsSerializer.Meta.model
        fields = [
            'id',
            'name',
            'detail',
            'newscategory_id',
            'count',
            'createdate',
            'createuser',
            'status',
            'newscategory',
        ]

    def get_newscategory(self, obj):
        try:
            category = TcdNewscategory.objects.get(id=obj.newscategory_id)
            return {
                "name": category.name,
                "order": category.order
            }
        except TcdNewscategory.DoesNotExist:
            return {
                "name": None,
                "order": None
            }


class DashboardParameterSerializer(serializers.Serializer):
    """
    Serializer for dashboard parameter
    """
    project_size = serializers.IntegerField(required=False, default=3)
    news_size = serializers.IntegerField(required=False, default=3)


class OverviewStatsSerializer(serializers.Serializer):
    """
    Serializer for overview statistics
    """
    total_projects = serializers.IntegerField()
    total_members = serializers.IntegerField()
    total_consultants = serializers.IntegerField()


class ConsultantTypeSerializer(serializers.Serializer):
    """
    Serializer for consultant type
    """
    independent_count = serializers.IntegerField()
    corporate_count = serializers.IntegerField()
    total_count = serializers.IntegerField()


class ProjectStatsSerializer(serializers.Serializer):
    """
    Serializer for project statistics
    """
    id = serializers.IntegerField()
    name = serializers.CharField()
    period_start = serializers.DateTimeField()
    period_end = serializers.DateTimeField()
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    create_date = serializers.DateTimeField()
    update_date = serializers.DateTimeField()


class TcdDashboardCategorySerializer(serializers.Serializer):
    """
    Serializer for dashboard category
    """
    id = serializers.IntegerField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    order = serializers.IntegerField()
    status = serializers.BooleanField()
    create_date = serializers.DateTimeField()
    update_date = serializers.DateTimeField()


class DashboardListParameterSerializer(serializers.Serializer):
    """
    Serializer for dashboard list parameter
    """
    dashboard_category_id = serializers.IntegerField(required=False, default=None)


class TcdDashboardSerializer(serializers.Serializer):
    """
    Serializer for dashboard
    """
    id = serializers.IntegerField()
    dashboard_category_id = serializers.IntegerField()
    name_th = serializers.CharField()
    name_en = serializers.CharField()
    embed_url = serializers.CharField()
    thumbnail = serializers.CharField()
    is_consult = serializers.BooleanField()
    is_matching = serializers.BooleanField()
    is_application = serializers.BooleanField()
    status = serializers.BooleanField()
    create_user_id = serializers.IntegerField()
    create_date = serializers.DateTimeField()
    update_user_id = serializers.IntegerField()
    update_date = serializers.DateTimeField()


class DashboardOverviewResponseSerializer(serializers.Serializer):
    """
    Serializer for dashboard overview response
    """
    overview = OverviewStatsSerializer()
    chart_data = serializers.ListField(child=serializers.DictField())
    service_info = TcdDashboardSerializer(many=True)
    project_stats = ProjectStatsSerializer(many=True)
    news = TcdNewsSerializer(many=True)
