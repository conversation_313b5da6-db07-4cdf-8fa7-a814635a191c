<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - {{ app_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        .logo.error {
            background: #f44336;
        }
        .logo.invalid {
            background: #ff9800;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .message {
            color: #666;
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.5;
        }
        .details {
            color: #888;
            font-size: 14px;
            margin-bottom: 30px;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .footer {
            margin-top: 30px;
            color: #aaa;
            font-size: 12px;
        }
        .manual-instructions {
            display: none;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }
        .manual-instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        .manual-instructions code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo {% if callback_status == 'error' %}error{% elif callback_status == 'invalid' %}invalid{% endif %}">
            {% if callback_status == 'success' %}✓{% elif callback_status == 'error' %}✗{% else %}?{% endif %}
        </div>
        
        <h1>{{ message }}</h1>
        <div class="message">{{ message_en|default:"" }}</div>
        <div class="details">{{ details|default:"" }}</div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>กำลังเปลี่ยนเส้นทาง...</p>
        </div>
        
        <div class="manual-instructions" id="manualInstructions">
            <h3>คำแนะนำสำหรับนักพัฒนา</h3>
            <p><strong>Authorization Code:</strong> <code id="authCode">{{ code|default:"ไม่มี" }}</code></p>
            <p><strong>State:</strong> <code id="authState">{{ state|default:"ไม่มี" }}</code></p>
            <p>ใช้ Authorization Code นี้กับ API endpoint: <code>/api/login-by-code/</code></p>
        </div>
        
        <div class="footer">
            {{ app_name }} - ThaID Authentication
        </div>
    </div>

    <script>
        // Data to pass to mobile app
        const callbackData = {
            status: '{{ callback_status }}',
            code: '{{ code|default:"" }}',
            state: '{{ state|default:"" }}',
            error: '{{ error|default:"" }}',
            error_description: '{{ error_description|default:"" }}'
        };
        
        // Log for debugging
        console.log('ThaID Callback Data:', callbackData);
        
        // Try to communicate with mobile app
        function notifyMobileApp() {
            try {
                // For iOS WKWebView
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.thaidCallback) {
                    window.webkit.messageHandlers.thaidCallback.postMessage(callbackData);
                    return true;
                }
                
                // For Android WebView
                if (window.Android && window.Android.onThaIDCallback) {
                    window.Android.onThaIDCallback(JSON.stringify(callbackData));
                    return true;
                }
                
                // For React Native WebView
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify(callbackData));
                    return true;
                }
                
                return false;
            } catch (e) {
                console.error('Error communicating with mobile app:', e);
                return false;
            }
        }
        
        // Auto-redirect for successful authentication
        if (callbackData.status === 'success' && callbackData.code) {
            document.getElementById('loading').style.display = 'block';
            
            // Try to notify mobile app
            const notified = notifyMobileApp();
            
            if (!notified) {
                // Fallback: try custom URL scheme
                setTimeout(() => {
                    const customUrl = `mcdc://thaid-callback?code=${encodeURIComponent(callbackData.code)}&state=${encodeURIComponent(callbackData.state || '')}`;
                    window.location.href = customUrl;
                    
                    // If custom URL doesn't work, show manual instructions
                    setTimeout(() => {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('manualInstructions').style.display = 'block';
                    }, 3000);
                }, 1000);
            }
        } else {
            // For error cases, still try to notify mobile app
            notifyMobileApp();
            
            // Show manual instructions for debugging
            if (callbackData.code) {
                setTimeout(() => {
                    document.getElementById('manualInstructions').style.display = 'block';
                }, 2000);
            }
        }
        
        // Make data available globally for manual access
        window.thaidCallbackData = callbackData;
    </script>
</body>
</html>
